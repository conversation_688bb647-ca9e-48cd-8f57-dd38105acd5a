/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/loop-builder/src/style.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-loop-builder-wrapper {
  box-sizing: border-box;
}
.eb-loop-builder-wrapper * {
  box-sizing: border-box;
}
.eb-loop-builder-wrapper .eb-loop-builder-template {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #f9f9f9;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-template:hover {
  border-color: #007cba;
  background: #f0f8ff;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.4;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #757575;
  line-height: 1.5;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-content .block-editor-inner-blocks .block-editor-block-list__layout {
  margin: 0;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.4;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #757575;
  line-height: 1.5;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-grid.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-grid.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item {
  border: 1px dashed #ccc;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item:hover {
  background: #eeeeee;
  border-color: #999;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item .eb-loop-builder-preview-placeholder {
  font-weight: 500;
}
.eb-loop-builder-wrapper .eb-loop-builder-container {
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-container.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
  width: 100%;
}
@media (max-width:768px) {
  .eb-loop-builder-wrapper .eb-loop-builder-container.grid {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 480px) {
  .eb-loop-builder-wrapper .eb-loop-builder-container.grid {
    grid-template-columns: 1fr;
  }
}
.eb-loop-builder-wrapper .eb-loop-builder-container.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items {
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item {
  position: relative;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-editable .block-editor-inner-blocks {
  border: 2px solid #007cba;
  border-radius: 4px;
  padding: 4px;
  background: rgba(0, 124, 186, 0.05);
  position: relative;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-editable .block-editor-inner-blocks::before {
  content: "Template (Editable)";
  position: absolute;
  top: -24px;
  left: 0;
  background: #007cba;
  color: white;
  padding: 4px 8px;
  border-radius: 4px 4px 0 0;
  font-size: 11px;
  font-weight: 500;
  z-index: 1;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px;
  background: #f9f9f9;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview::before {
  content: "Preview";
  position: absolute;
  top: -20px;
  left: 0;
  background: #666;
  color: white;
  padding: 2px 6px;
  border-radius: 4px 4px 0 0;
  font-size: 10px;
  font-weight: 500;
  z-index: 1;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  pointer-events: none;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item .eb-loop-builder-block-preview {
  cursor: default;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  border-radius: 4px;
  padding: 4px;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-active .block-editor-inner-blocks {
  border: 2px solid #007cba;
  border-radius: 4px;
  padding: 4px;
  background: rgba(0, 124, 186, 0.05);
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination {
  margin-top: 30px;
  text-align: center;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination {
  display: inline-flex;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-decoration: none;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers:hover {
  background: #007cba;
  color: white;
  border-color: #007cba;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.current {
  background: #007cba;
  color: white;
  border-color: #007cba;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.dots {
  border: none;
  cursor: default;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.dots:hover {
  background: transparent;
  color: #333;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn {
  display: inline-block;
  padding: 12px 24px;
  background: #007cba;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn:hover {
  background: #005a87;
  transform: translateY(-1px);
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}
.eb-loop-builder-wrapper .eb-loop-builder-loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
.eb-loop-builder-wrapper .eb-loop-builder-loading .spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007cba;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.eb-loop-builder-wrapper .eb-loop-builder-no-posts {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

/*# sourceMappingURL=style.css.map*//*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/post-template/src/style.scss ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-template-wrapper {
  position: relative;
}
.eb-post-template-wrapper .eb-post-template-container.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
.eb-post-template-wrapper .eb-post-template-container.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.eb-post-template-wrapper .eb-post-template-items {
  display: contents;
}
.eb-post-template-wrapper .eb-post-template-item {
  position: relative;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-editable {
  border: 2px dashed transparent;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-editable:hover {
  border-color: #007cba;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-preview {
  opacity: 0.8;
  cursor: pointer;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-preview:hover {
  opacity: 1;
}
.eb-post-template-wrapper .eb-post-template-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-loading .components-spinner {
  margin-bottom: 16px;
}
.eb-post-template-wrapper .eb-post-template-loading p {
  margin: 0;
  color: #666;
  font-style: italic;
}
.eb-post-template-wrapper .eb-post-template-template {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header {
  margin-bottom: 20px;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header h4 {
  margin: 0 0 8px 0;
  color: #1e1e1e;
  font-size: 16px;
  font-weight: 600;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-content .block-editor-inner-blocks {
  text-align: left;
}
.eb-post-template-wrapper .eb-post-template-no-results {
  padding: 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-no-results p {
  margin: 0;
  font-size: 14px;
}
.eb-post-template-wrapper .eb-post-template-placeholder {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-placeholder .eb-post-template-notice h4 {
  margin: 0 0 8px 0;
  color: #1e1e1e;
  font-size: 16px;
  font-weight: 600;
}
.eb-post-template-wrapper .eb-post-template-placeholder .eb-post-template-notice p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}
.eb-post-template-wrapper .eb-post-template-block-preview {
  pointer-events: none;
}
.eb-post-template-wrapper .eb-post-template-block-preview:hover {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

.block-editor-block-list__layout .eb-post-template-wrapper {
  margin-bottom: 20px;
}

.eb-post-template-context-info .eb-control-label {
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

/*# sourceMappingURL=style.css.map*//*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/advanced-image/src/style.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */
.eb-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.eb-image-wrapper figcaption {
  text-align: center;
}
.eb-image-wrapper .eb-image-wrapper-inner {
  position: relative;
  max-width: 100%;
  height: 100%;
  overflow: hidden;
}
.eb-image-wrapper .eb-image-wrapper-inner div, .eb-image-wrapper .eb-image-wrapper-inner img {
  transition: transform 0.3s;
  width: 100%;
  height: 100%;
}
.eb-image-wrapper .eb-image-link {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-triangle {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-rhombus {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-octagon {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-circle {
  border-radius: 50%;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-square {
  border-radius: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-rounded {
  border-radius: 15px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-top figcaption {
  left: 0px;
  top: 0px;
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-middle figcaption {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-bottom figcaption {
  left: 0px;
  bottom: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-top figcaption {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-middle figcaption {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-bottom figcaption {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-top figcaption {
  right: 0px;
  top: 0px;
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-middle figcaption {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-bottom figcaption {
  right: 0px;
  bottom: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1 figcaption {
  background: #f9fafb;
  color: #101828;
  bottom: 0;
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-2 {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-2.top {
  flex-direction: column-reverse;
}

.eb-advanced-image-wrapper {
  margin: 0;
  /* Zoom In #2 */
}
.eb-advanced-image-wrapper.img-style-triangle .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-triangle .image-wrapper {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
.eb-advanced-image-wrapper.img-style-rhombus .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-rhombus .image-wrapper {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.eb-advanced-image-wrapper.img-style-octagon .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-octagon .image-wrapper {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}
.eb-advanced-image-wrapper.caption-style-1 {
  overflow: visible;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-top figcaption {
  left: 0px;
  top: 0px;
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-middle figcaption {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-bottom figcaption {
  left: 0px;
  bottom: 0px;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-top figcaption {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-middle figcaption {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-bottom figcaption {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-top figcaption {
  right: 0px;
  top: 0px;
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-middle figcaption {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-bottom figcaption {
  right: 0px;
  bottom: 0px;
}
.eb-advanced-image-wrapper.caption-style-1 figcaption {
  background: rgba(0, 0, 0, 0.8);
  bottom: 0;
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}
.eb-advanced-image-wrapper.caption-style-2 {
  display: flex;
  flex-direction: column;
}
.eb-advanced-image-wrapper.caption-style-2.top {
  flex-direction: column-reverse;
}
.eb-advanced-image-wrapper .eb-image-wrapper, .eb-advanced-image-wrapper .image-wrapper {
  overflow: hidden;
  position: relative;
  max-width: 100%;
}
.eb-advanced-image-wrapper img {
  display: block;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  height: 100%;
  margin: 0 auto !important;
}
.eb-advanced-image-wrapper .eb-image-caption-wrap {
  line-height: 1;
  padding: 15px;
}
.eb-advanced-image-wrapper.zoom-in img {
  transform: scale(1);
}
.eb-advanced-image-wrapper.zoom-in .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.zoom-in .image-wrapper:hover img {
  transform: scale(1.3);
}
.eb-advanced-image-wrapper.zoom-out img {
  transform: scale(1.5);
}
.eb-advanced-image-wrapper.zoom-out .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.zoom-out .image-wrapper:hover img {
  transform: scale(1);
}
.eb-advanced-image-wrapper.slide img {
  margin-left: 30px !important;
  transform: scale(1.3);
  transition: 0.3s ease-in-out !important;
}
.eb-advanced-image-wrapper.slide .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.slide .image-wrapper:hover img {
  margin-left: 0 !important;
}
.eb-advanced-image-wrapper.blur img {
  filter: blur(3px);
}
.eb-advanced-image-wrapper.blur .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.blur .image-wrapper:hover img {
  filter: blur(0);
}
.eb-advanced-image-wrapper .eb-advimg-link {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.wp-block-essential-blocks-advanced-image .eb-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.feature-image-placeholder {
  width: 100%;
  background: #EAEEF9;
  text-align: center;
}
.feature-image-placeholder img {
  width: 300px;
}

/*# sourceMappingURL=style.css.map*//*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/advanced-heading/src/style.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-advance-heading-wrapper a {
  color: inherit;
}
.eb-advance-heading-wrapper > * {
  transition: all 0.3s ease-in-out;
}
.eb-advance-heading-wrapper .eb-ah-title > * {
  display: inline;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  line-height: inherit;
}
.eb-advance-heading-wrapper.marquee {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  box-sizing: border-box;
  position: relative;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title {
  display: inline-block;
  animation-name: marquee;
  animation-timing-function: linear;
  animation-duration: 10s;
  animation-iteration-count: infinite;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title > * {
  white-space: nowrap !important;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title:hover {
  animation-play-state: paused;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title {
  -webkit-box-reflect: below -20px linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  line-height: 1.5em;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a) {
  animation: waviy 1s;
  display: inline-block;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a):nth-child(2) {
  animation-delay: 1s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a):nth-child(3) {
  animation-delay: 2s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a {
  -webkit-box-reflect: below -20px linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  line-height: 1.5em;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > * {
  animation: waviy 1s;
  display: inline-block;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > *:nth-child(2) {
  animation-delay: 1s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > *:nth-child(3) {
  animation-delay: 2s;
}
.eb-advance-heading-wrapper .eb-ah-separator.icon > * {
  font-size: inherit;
}
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes waviy {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

/*# sourceMappingURL=style.css.map*//*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/text/src/style.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/post-meta/src/style.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-meta-wrapper {
  box-sizing: border-box;
}
.eb-post-meta-wrapper .eb-post-metadata {
  display: flex;
  align-items: center;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-stacked {
  flex-direction: column;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-inline {
  flex-direction: row;
}
.eb-post-meta-wrapper .eb-post-metadata-label {
  font-weight: bold;
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-metadata-value {
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-meta-stacked .eb-post-metadata-item {
  display: block;
}
.eb-post-meta-wrapper .eb-post-meta-inline .eb-post-metadata-item {
  display: inline-block;
}

/*# sourceMappingURL=style.css.map*//*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/post-grid/src/style.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-grid-wrapper {
  z-index: 1;
}
.eb-post-grid-wrapper a,
.eb-post-grid-wrapper a.ebpg-grid-post-link,
.eb-post-grid-wrapper .ebpg-posted-by a {
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}
.eb-post-grid-wrapper .ebpg-grid-post-holder a.ebpg-post-link-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
.eb-post-grid-wrapper .ebpg-entry-thumbnail {
  position: relative;
  line-height: 0;
}
.eb-post-grid-wrapper .ebpg-entry-thumbnail img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-grid-wrapper .ebpg-entry-thumbnail:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s ease-in-out;
}
.eb-post-grid-wrapper .ebpg-entry-thumbnail:hover:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-grid-wrapper .ebpg-entry-wrapper {
  display: flex;
  flex-direction: column;
}
.eb-post-grid-wrapper .ebpg-entry-content {
  display: flex;
  flex-direction: column;
}
.eb-post-grid-wrapper .ebpg-entry-meta {
  display: flex;
  align-items: center;
}
.eb-post-grid-wrapper .ebpg-entry-meta .ebpg-entry-meta-items {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.eb-post-grid-wrapper .ebpg-author-avatar a {
  display: block;
}
.eb-post-grid-wrapper .ebpg-author-avatar a img {
  height: 50px;
  width: 50px;
  max-height: 50px;
  max-width: 50px;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-grid-wrapper .ebpg-meta a {
  padding: 2px 5px;
  border-radius: 3px;
  margin-right: 5px;
  margin-bottom: 5px;
  display: inline-block;
  text-decoration: none;
}
.eb-post-grid-wrapper .ebpg-meta.ebpg-dynamic-values {
  padding: 2px 5px;
  border-radius: 3px;
  margin-right: 5px;
  margin-bottom: 5px;
  display: inline-block;
  text-decoration: none;
}
.eb-post-grid-wrapper .ebpg-read-time i {
  margin-right: 5px;
}
.eb-post-grid-wrapper .ebpg-post-grid-column {
  overflow: hidden;
  z-index: 1;
}
.eb-post-grid-wrapper .ebpg-readmore-btn a {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder {
  display: flex;
  gap: 10px;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-media {
  width: 40%;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper {
  width: 60%;
  display: flex;
  flex-flow: column;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-header-meta {
  order: 1;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-header {
  order: 2;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-content {
  order: 3;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-footer-meta {
  order: 4;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-author-avatar a {
  line-height: 1;
  max-height: 25px;
}
.eb-post-grid-wrapper.style-4 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-author-avatar a img {
  height: 25px;
  width: 25px;
  max-height: 100%;
  max-width: 100%;
}
.eb-post-grid-wrapper.style-5 .ebpg-grid-post-holder {
  position: relative;
}
.eb-post-grid-wrapper.style-5 .ebpg-grid-post-holder .ebpg-entry-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
}
.eb-post-grid-wrapper.style-5 .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta {
  z-index: 999;
  line-height: 1;
}
.eb-post-grid-wrapper.style-5 .ebpg-grid-post-holder .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-grid-wrapper.style-5 .ebpg-grid-post-holder:hover .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-grid-wrapper .ebpg-pagination {
  grid-column: 1/-1;
}
.eb-post-grid-wrapper .ebpg-pagination button:disabled {
  opacity: 0.5;
  background-color: #e8e8e8;
  color: #333;
}
.eb-post-grid-wrapper .ebpg-pagination button:disabled:hover {
  cursor: unset;
  background-color: #e8e8e8;
  color: #333;
}
.eb-post-grid-wrapper .ebpg-pagination.prev-next-btn .ebpg-pagination-item,
.eb-post-grid-wrapper .ebpg-pagination.prev-next-btn .ebpg-pagination-item-separator {
  display: none !important;
}
.eb-post-grid-wrapper .ebpg-pagination .show {
  display: inline-block;
}
.eb-post-grid-wrapper .ebpg-pagination .hide {
  display: none;
}
.eb-post-grid-wrapper .ebpg-pagination .ebpg-pagination-item-separator {
  border: none !important;
}

.eb-post-grid-category-filter {
  grid-column: 1/-1;
  width: 100%;
  margin-bottom: 20px;
}
.eb-post-grid-category-filter .ebpg-category-filter-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}
.eb-post-grid-category-filter .ebpg-category-filter-list li {
  width: auto;
  padding: 10px 20px;
  border-radius: 3px;
  transition: all 0.3s ease-in-out;
}
.eb-post-grid-category-filter .ebpg-category-filter-list li.active {
  background-color: #d18df1;
  color: #fff;
}
.eb-post-grid-category-filter .ebpg-category-filter-list li:hover {
  cursor: pointer;
  background-color: #d18df1;
  color: #fff;
}

/*# sourceMappingURL=style.css.map*//*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks-extends/post-grid/src/style.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper {
  display: block !important;
  position: relative !important;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder {
  position: relative;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder:hover .ebpg-entry-wrapper > * {
  transform: translate(0px, 0px);
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder:hover .ebpg-entry-wrapper .ebpg-entry-meta {
  opacity: 1;
  visibility: visible;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta {
  z-index: 999;
  line-height: 1;
  opacity: 0;
  visibility: hidden;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-header-meta {
  order: 1;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-header {
  order: 2;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-content {
  order: 3;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-footer-meta {
  order: 4;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-author-avatar img {
  width: 24px;
  height: 24px;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-wrapper > * {
  transform: translate(0px, 20px);
  transition: ease-in 200ms;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-thumbnail img {
  height: auto;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-grid-wrapper.pro-style-6 .eb-post-grid-posts-wrapper .ebpg-grid-post-holder:hover .ebpg-entry-thumbnail:after {
  visibility: visible;
  opacity: 1;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child {
  grid-column: 1/-1;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child .ebpg-entry-content {
  order: 4;
  width: 100%;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child .ebpg-entry-content .ebpg-grid-post-excerpt {
  display: none;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper {
  display: flex;
  flex-flow: wrap;
  justify-content: space-between;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper .ebpg-entry-header {
  width: 100%;
  margin-top: 20px;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper .ebpg-entry-header .ebpg-entry-title a {
  font-size: 30px;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:not(:first-child) .ebpg-entry-wrapper {
  box-shadow: 0px 5px 10px rgba(0, 1, 35, 0.08);
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column:not(:first-child) .ebpg-entry-meta {
  display: none;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder {
  position: relative;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-media {
  z-index: 1;
  position: relative;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper {
  background-color: #fff;
  z-index: 999;
  position: absolute;
  width: 80%;
  left: 10%;
  bottom: 0;
  padding: 15px;
  border-radius: 5px;
  box-sizing: border-box;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-author-avatar a img {
  height: 30px;
  width: 30px;
  max-height: 30px;
  max-width: 30px;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-readmore-btn {
  visibility: hidden;
  opacity: 0;
  margin-bottom: -30px;
  transform: translate(0px, 0px);
  transition: ease-in 200ms;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-readmore-btn a::after {
  content: "\f342";
  font-family: dashicons;
  font-size: 1.2em;
  margin: 0px 0px 0px 5px;
  line-height: 1;
  transform: rotate(45deg);
  display: inline-block;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder:hover .ebpg-entry-wrapper .ebpg-readmore-btn {
  visibility: visible;
  opacity: 1;
  margin-bottom: 0;
}
.eb-post-grid-wrapper.pro-style-7 .ebpg-post-grid-column .ebpg-grid-post-holder:hover .ebpg-entry-thumbnail:after {
  opacity: 1;
  visibility: visible;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:first-child {
  grid-column: 1/-1;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:first-child .ebpg-entry-media {
  width: 60%;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translate(0px, -50%);
  width: 60%;
  height: auto;
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(0, 1, 35, 0.08);
  border-radius: 0px !important;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper .ebpg-entry-header {
  width: 100%;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:first-child .ebpg-entry-wrapper .ebpg-entry-header .ebpg-entry-title a {
  font-size: 30px;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:not(:first-child) .ebpg-grid-post-holder {
  transition: ease-in-out 300ms;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column:not(:first-child) .ebpg-grid-post-holder:hover {
  box-shadow: 0px 5px 10px rgba(0, 1, 35, 0.08);
  background-color: #fff;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder {
  position: relative;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-media {
  z-index: 1;
  position: relative;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper {
  z-index: 999;
  padding: 15px;
  border-radius: 5px;
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-header-meta {
  order: 1;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-header {
  order: 2;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-content {
  order: 3;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-entry-meta.ebpg-footer-meta {
  order: 4;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder .ebpg-entry-wrapper .ebpg-author-avatar a img {
  height: 30px;
  width: 30px;
  max-height: 30px;
  max-width: 30px;
}
.eb-post-grid-wrapper.pro-style-8 .ebpg-post-grid-column .ebpg-grid-post-holder:hover .ebpg-entry-thumbnail:after {
  opacity: 1;
  visibility: visible;
}
.eb-post-grid-wrapper .eb-post-grid-search {
  box-sizing: border-box;
  grid-column: 1/-1;
  width: 100%;
  margin-bottom: 20px;
  position: relative;
  background-color: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 8px;
}
.eb-post-grid-wrapper .eb-post-grid-search-form {
  display: flex;
  flex-wrap: nowrap;
  max-width: 100%;
  align-items: center;
  padding: 10px;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-loader {
  width: 20px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  z-index: 4;
  display: none;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-close {
  position: absolute;
  top: 50%;
  right: 20px;
  z-index: 4;
  display: none;
  transform: translateY(-50%);
  font-size: 15px;
  height: 25px;
  width: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  background: rgba(220, 68, 68, 0.1);
  color: #dc4444;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap {
  flex-grow: 1;
  min-width: 3em;
  position: relative;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input {
  border: none;
  padding: 8px;
  margin-left: 0;
  margin-right: 0;
  text-decoration: unset !important;
  width: 100%;
  background-color: inherit;
  border-radius: 0;
  caret-color: inherit;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:auto-fill, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:-internal-autofill-selected {
  background-color: inherit !important;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:focus, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:hover, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:-webkit-autofill, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:-webkit-autofill:hover, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input:-webkit-autofill:focus {
  color: inherit;
  border: none;
  outline: none;
  box-shadow: none;
  background-color: inherit;
  -webkit-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input[type=search]::-webkit-search-decoration, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input[type=search]::-webkit-search-cancel-button, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input[type=search]::-webkit-search-results-button, .eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-input-wrap input[type=search]::-webkit-search-results-decoration {
  display: none;
}
.eb-post-grid-wrapper .eb-post-grid-search-form .eb-post-grid-search-icon {
  width: 20px;
  color: inherit;
  margin-right: 10px;
  margin-left: 10px;
}
.eb-post-grid-wrapper .eb-post-grid-search-result {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid #e0e5f8;
  box-shadow: 0px 4px 8px rgba(165, 174, 212, 0.15);
  border-radius: 5px;
  padding: 20px 30px;
  display: none;
  z-index: 999;
  overflow-y: auto;
  max-height: 50vh;
  box-sizing: border-box;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item:last-child {
  margin-bottom: 0 !important;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item .eb-post-grid-search-item-thumb {
  flex: 0 0 120px;
  width: 120px;
  height: 70px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 20px;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item .eb-post-grid-search-item-thumb img {
  height: auto;
  width: 200px;
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item .eb-post-grid-search-item-content h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 400;
  color: #434872;
  margin-bottom: 5px;
  transition: color, 0.3s ease;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item .eb-post-grid-search-item-content h4 strong {
  font-weight: 700;
}
.eb-post-grid-wrapper .eb-post-grid-search-content-item .eb-post-grid-search-item-content p {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  color: #b2b9c6;
}
.eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 10px 35px 8px rgba(0, 9, 78, 0.1);
}
.eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .ebpg-category-filter-list {
  justify-content: flex-start;
  width: 60%;
}
.eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .eb-post-grid-search {
  width: 40%;
  margin: 0;
  border: 0;
}
.eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .eb-post-grid-search .eb-post-grid-search-form {
  border: 0;
  padding: 0;
}
.eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .eb-post-grid-search .eb-post-grid-search-form .eb-post-grid-search-field {
  border-left: 1px solid #d7d7d7;
}

@media only screen and (max-width:1024px) {
  .eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search {
    flex-wrap: wrap;
  }
  .eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .ebpg-category-filter-list,
  .eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .eb-post-grid-search {
    width: 100%;
  }
  .eb-post-grid-wrapper .eb-post-grid-category-filter.eb-show-search .eb-post-grid-search {
    border: 1px solid #c9d8eb;
    margin-top: 10px;
  }
}

/*# sourceMappingURL=style.css.map*/