/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/loop-builder/src/style.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-loop-builder-wrapper {
  box-sizing: border-box;
}
.eb-loop-builder-wrapper * {
  box-sizing: border-box;
}
.eb-loop-builder-wrapper .eb-loop-builder-template {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #f9f9f9;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-template:hover {
  border-color: #007cba;
  background: #f0f8ff;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.4;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #757575;
  line-height: 1.5;
}
.eb-loop-builder-wrapper .eb-loop-builder-template .eb-loop-builder-template-content .block-editor-inner-blocks .block-editor-block-list__layout {
  margin: 0;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.4;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #757575;
  line-height: 1.5;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-grid.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-grid.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item {
  border: 1px dashed #ccc;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item:hover {
  background: #eeeeee;
  border-color: #999;
}
.eb-loop-builder-wrapper .eb-loop-builder-preview .eb-loop-builder-preview-item .eb-loop-builder-preview-placeholder {
  font-weight: 500;
}
.eb-loop-builder-wrapper .eb-loop-builder-container {
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-container.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
  width: 100%;
}
@media (max-width:768px) {
  .eb-loop-builder-wrapper .eb-loop-builder-container.grid {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 480px) {
  .eb-loop-builder-wrapper .eb-loop-builder-container.grid {
    grid-template-columns: 1fr;
  }
}
.eb-loop-builder-wrapper .eb-loop-builder-container.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items {
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items.grid {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item {
  position: relative;
  width: 100%;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-editable .block-editor-inner-blocks {
  border: 2px solid #007cba;
  border-radius: 4px;
  padding: 4px;
  background: rgba(0, 124, 186, 0.05);
  position: relative;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-editable .block-editor-inner-blocks::before {
  content: "Template (Editable)";
  position: absolute;
  top: -24px;
  left: 0;
  background: #007cba;
  color: white;
  padding: 4px 8px;
  border-radius: 4px 4px 0 0;
  font-size: 11px;
  font-weight: 500;
  z-index: 1;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px;
  background: #f9f9f9;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview::before {
  content: "Preview";
  position: absolute;
  top: -20px;
  left: 0;
  background: #666;
  color: white;
  padding: 2px 6px;
  border-radius: 4px 4px 0 0;
  font-size: 10px;
  font-weight: 500;
  z-index: 1;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-preview .eb-loop-builder-block-preview::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  pointer-events: none;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item .eb-loop-builder-block-preview {
  cursor: default;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  border-radius: 4px;
  padding: 4px;
}
.eb-loop-builder-wrapper .eb-loop-builder-items .eb-loop-item.eb-loop-item-active .block-editor-inner-blocks {
  border: 2px solid #007cba;
  border-radius: 4px;
  padding: 4px;
  background: rgba(0, 124, 186, 0.05);
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination {
  margin-top: 30px;
  text-align: center;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination {
  display: inline-flex;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-decoration: none;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers:hover {
  background: #007cba;
  color: white;
  border-color: #007cba;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.current {
  background: #007cba;
  color: white;
  border-color: #007cba;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.dots {
  border: none;
  cursor: default;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .pagination .page-numbers.dots:hover {
  background: transparent;
  color: #333;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn {
  display: inline-block;
  padding: 12px 24px;
  background: #007cba;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn:hover {
  background: #005a87;
  transform: translateY(-1px);
}
.eb-loop-builder-wrapper .eb-loop-builder-pagination .eb-load-more-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}
.eb-loop-builder-wrapper .eb-loop-builder-loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
.eb-loop-builder-wrapper .eb-loop-builder-loading .spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007cba;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.eb-loop-builder-wrapper .eb-loop-builder-no-posts {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

/*# sourceMappingURL=style.css.map*//*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/post-template/src/style.scss ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-template-wrapper {
  position: relative;
}
.eb-post-template-wrapper .eb-post-template-container.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
.eb-post-template-wrapper .eb-post-template-container.list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.eb-post-template-wrapper .eb-post-template-items {
  display: contents;
}
.eb-post-template-wrapper .eb-post-template-item {
  position: relative;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-editable {
  border: 2px dashed transparent;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-editable:hover {
  border-color: #007cba;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-preview {
  opacity: 0.8;
  cursor: pointer;
}
.eb-post-template-wrapper .eb-post-template-item.eb-post-template-item-preview:hover {
  opacity: 1;
}
.eb-post-template-wrapper .eb-post-template-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-loading .components-spinner {
  margin-bottom: 16px;
}
.eb-post-template-wrapper .eb-post-template-loading p {
  margin: 0;
  color: #666;
  font-style: italic;
}
.eb-post-template-wrapper .eb-post-template-template {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header {
  margin-bottom: 20px;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header h4 {
  margin: 0 0 8px 0;
  color: #1e1e1e;
  font-size: 16px;
  font-weight: 600;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}
.eb-post-template-wrapper .eb-post-template-template .eb-post-template-template-content .block-editor-inner-blocks {
  text-align: left;
}
.eb-post-template-wrapper .eb-post-template-no-results {
  padding: 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-no-results p {
  margin: 0;
  font-size: 14px;
}
.eb-post-template-wrapper .eb-post-template-placeholder {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
}
.eb-post-template-wrapper .eb-post-template-placeholder .eb-post-template-notice h4 {
  margin: 0 0 8px 0;
  color: #1e1e1e;
  font-size: 16px;
  font-weight: 600;
}
.eb-post-template-wrapper .eb-post-template-placeholder .eb-post-template-notice p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}
.eb-post-template-wrapper .eb-post-template-block-preview {
  pointer-events: none;
}
.eb-post-template-wrapper .eb-post-template-block-preview:hover {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

.block-editor-block-list__layout .eb-post-template-wrapper {
  margin-bottom: 20px;
}

.eb-post-template-context-info .eb-control-label {
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

/*# sourceMappingURL=style.css.map*//*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/advanced-image/src/style.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */
.eb-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.eb-image-wrapper figcaption {
  text-align: center;
}
.eb-image-wrapper .eb-image-wrapper-inner {
  position: relative;
  max-width: 100%;
  height: 100%;
  overflow: hidden;
}
.eb-image-wrapper .eb-image-wrapper-inner div, .eb-image-wrapper .eb-image-wrapper-inner img {
  transition: transform 0.3s;
  width: 100%;
  height: 100%;
}
.eb-image-wrapper .eb-image-link {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-triangle {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-rhombus {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-octagon {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-circle {
  border-radius: 50%;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-square {
  border-radius: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-style-rounded {
  border-radius: 15px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-top figcaption {
  left: 0px;
  top: 0px;
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-middle figcaption {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-left.eb-caption-vertical-bottom figcaption {
  left: 0px;
  bottom: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-top figcaption {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-middle figcaption {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-center.eb-caption-vertical-bottom figcaption {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-top figcaption {
  right: 0px;
  top: 0px;
  bottom: auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-middle figcaption {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1.eb-caption-horizontal-right.eb-caption-vertical-bottom figcaption {
  right: 0px;
  bottom: 0px;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-1 figcaption {
  background: #f9fafb;
  color: #101828;
  bottom: 0;
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-2 {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}
.eb-image-wrapper .eb-image-wrapper-inner.eb-img-caption-style-2.top {
  flex-direction: column-reverse;
}

.eb-advanced-image-wrapper {
  margin: 0;
  /* Zoom In #2 */
}
.eb-advanced-image-wrapper.img-style-triangle .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-triangle .image-wrapper {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
.eb-advanced-image-wrapper.img-style-rhombus .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-rhombus .image-wrapper {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.eb-advanced-image-wrapper.img-style-octagon .eb-image-wrapper, .eb-advanced-image-wrapper.img-style-octagon .image-wrapper {
  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}
.eb-advanced-image-wrapper.caption-style-1 {
  overflow: visible;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-top figcaption {
  left: 0px;
  top: 0px;
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-middle figcaption {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-left.caption-vertical-bottom figcaption {
  left: 0px;
  bottom: 0px;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-top figcaption {
  left: 50%;
  top: 0px;
  transform: translateX(-50%);
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-middle figcaption {
  left: 50%;
  top: 50%;
  bottom: auto !important;
  transform: translate(-50%, -50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-center.caption-vertical-bottom figcaption {
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-top figcaption {
  right: 0px;
  top: 0px;
  bottom: auto;
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-middle figcaption {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.eb-advanced-image-wrapper.caption-style-1.caption-horizontal-right.caption-vertical-bottom figcaption {
  right: 0px;
  bottom: 0px;
}
.eb-advanced-image-wrapper.caption-style-1 figcaption {
  background: rgba(0, 0, 0, 0.8);
  bottom: 0;
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}
.eb-advanced-image-wrapper.caption-style-2 {
  display: flex;
  flex-direction: column;
}
.eb-advanced-image-wrapper.caption-style-2.top {
  flex-direction: column-reverse;
}
.eb-advanced-image-wrapper .eb-image-wrapper, .eb-advanced-image-wrapper .image-wrapper {
  overflow: hidden;
  position: relative;
  max-width: 100%;
}
.eb-advanced-image-wrapper img {
  display: block;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  height: 100%;
  margin: 0 auto !important;
}
.eb-advanced-image-wrapper .eb-image-caption-wrap {
  line-height: 1;
  padding: 15px;
}
.eb-advanced-image-wrapper.zoom-in img {
  transform: scale(1);
}
.eb-advanced-image-wrapper.zoom-in .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.zoom-in .image-wrapper:hover img {
  transform: scale(1.3);
}
.eb-advanced-image-wrapper.zoom-out img {
  transform: scale(1.5);
}
.eb-advanced-image-wrapper.zoom-out .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.zoom-out .image-wrapper:hover img {
  transform: scale(1);
}
.eb-advanced-image-wrapper.slide img {
  margin-left: 30px !important;
  transform: scale(1.3);
  transition: 0.3s ease-in-out !important;
}
.eb-advanced-image-wrapper.slide .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.slide .image-wrapper:hover img {
  margin-left: 0 !important;
}
.eb-advanced-image-wrapper.blur img {
  filter: blur(3px);
}
.eb-advanced-image-wrapper.blur .eb-image-wrapper:hover img, .eb-advanced-image-wrapper.blur .image-wrapper:hover img {
  filter: blur(0);
}
.eb-advanced-image-wrapper .eb-advimg-link {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.wp-block-essential-blocks-advanced-image .eb-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.feature-image-placeholder {
  width: 100%;
  background: #EAEEF9;
  text-align: center;
}
.feature-image-placeholder img {
  width: 300px;
}

/*# sourceMappingURL=style.css.map*//*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/advanced-heading/src/style.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-advance-heading-wrapper a {
  color: inherit;
}
.eb-advance-heading-wrapper > * {
  transition: all 0.3s ease-in-out;
}
.eb-advance-heading-wrapper .eb-ah-title > * {
  display: inline;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  line-height: inherit;
}
.eb-advance-heading-wrapper.marquee {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  box-sizing: border-box;
  position: relative;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title {
  display: inline-block;
  animation-name: marquee;
  animation-timing-function: linear;
  animation-duration: 10s;
  animation-iteration-count: infinite;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title > * {
  white-space: nowrap !important;
}
.eb-advance-heading-wrapper.marquee .eb-ah-title:hover {
  animation-play-state: paused;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title {
  -webkit-box-reflect: below -20px linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  line-height: 1.5em;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a) {
  animation: waviy 1s;
  display: inline-block;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a):nth-child(2) {
  animation-delay: 1s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title > *:not(a):nth-child(3) {
  animation-delay: 2s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a {
  -webkit-box-reflect: below -20px linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  line-height: 1.5em;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > * {
  animation: waviy 1s;
  display: inline-block;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > *:nth-child(2) {
  animation-delay: 1s;
}
.eb-advance-heading-wrapper.waviy .eb-ah-title a > *:nth-child(3) {
  animation-delay: 2s;
}
.eb-advance-heading-wrapper .eb-ah-separator.icon > * {
  font-size: inherit;
}
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes waviy {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

/*# sourceMappingURL=style.css.map*//*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/text/src/style.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/post-meta/src/style.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-post-meta-wrapper {
  box-sizing: border-box;
}
.eb-post-meta-wrapper .eb-post-metadata {
  display: flex;
  align-items: center;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-stacked {
  flex-direction: column;
}
.eb-post-meta-wrapper .eb-post-metadata.eb-post-meta-inline {
  flex-direction: row;
}
.eb-post-meta-wrapper .eb-post-metadata-label {
  font-weight: bold;
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-metadata-value {
  color: #000000;
}
.eb-post-meta-wrapper .eb-post-meta-stacked .eb-post-metadata-item {
  display: block;
}
.eb-post-meta-wrapper .eb-post-meta-inline .eb-post-metadata-item {
  display: inline-block;
}

/*# sourceMappingURL=style.css.map*//*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.91.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_yssjhdo6ab2kxp7ctqneboc7li/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@12.6.0_sass@1.77.4+webpack@5.91.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/blocks/taxonomy/src/style.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.eb-taxonomies-wrapper.display-inline {
  display: flex;
}
.eb-taxonomies-wrapper.display-block {
  display: block;
}
.eb-taxonomies-wrapper.display-block .eb-tax-wrap {
  flex-direction: column;
}
.eb-taxonomies-wrapper .eb-tax-wrap {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
}
.eb-taxonomies-wrapper .eb-tax-wrap span {
  padding: 0;
  margin: 0;
}
.eb-taxonomies-wrapper .eb-tax-wrap a {
  text-decoration: none;
  display: inline-block;
}
.eb-taxonomies-wrapper .eb-tax-separator:last-child {
  display: none;
}

/*# sourceMappingURL=style.css.map*/