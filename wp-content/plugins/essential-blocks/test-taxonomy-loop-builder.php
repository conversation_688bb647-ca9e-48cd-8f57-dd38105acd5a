<?php
/**
 * Test file for Taxonomy Block Loop Builder Integration
 * 
 * This file tests the taxonomy block's integration with Loop Builder
 * to ensure it properly detects context and displays taxonomies correctly.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Taxonomy Block Loop Builder Integration
 */
class TaxonomyLoopBuilderTest {
    
    /**
     * Run all tests
     */
    public static function run_tests() {
        echo "<h2>Taxonomy Block Loop Builder Integration Tests</h2>\n";
        
        self::test_block_json_configuration();
        self::test_context_detection();
        self::test_php_backend_integration();
        
        echo "<h3>✅ All tests completed!</h3>\n";
    }
    
    /**
     * Test block.json configuration
     */
    private static function test_block_json_configuration() {
        echo "<h3>1. Testing block.json Configuration</h3>\n";
        
        $block_json_path = WP_PLUGIN_DIR . '/essential-blocks/src/blocks/taxonomy/block.json';
        
        if (file_exists($block_json_path)) {
            $block_json = file_get_contents($block_json_path);
            $config = json_decode($block_json, true);
            
            if (isset($config['usesContext'])) {
                echo "✅ Taxonomy block usesContext found\n";
                
                // Check for required context keys
                $required_keys = [
                    'essential-blocks/postId',
                    'essential-blocks/postType',
                    'essential-blocks/isLoopBuilder'
                ];
                
                foreach ($required_keys as $key) {
                    if (in_array($key, $config['usesContext'])) {
                        echo "✅ Context key '{$key}' found\n";
                    } else {
                        echo "❌ Context key '{$key}' missing\n";
                    }
                }
            } else {
                echo "❌ Taxonomy block usesContext missing\n";
            }
        } else {
            echo "❌ Could not find taxonomy block.json\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test context detection logic
     */
    private static function test_context_detection() {
        echo "<h3>2. Testing Context Detection Logic</h3>\n";
        
        // Test Loop Builder context detection
        $loop_context = [
            'essential-blocks/isLoopBuilder' => true,
            'essential-blocks/postId' => 123,
            'essential-blocks/postType' => 'post'
        ];
        
        $taxonomy_block = new \EssentialBlocks\Blocks\Taxonomy();
        
        // Use reflection to test private method
        $reflection = new ReflectionClass($taxonomy_block);
        $method = $reflection->getMethod('is_in_loop_builder_context');
        $method->setAccessible(true);
        
        $is_loop_context = $method->invoke($taxonomy_block, $loop_context);
        
        if ($is_loop_context) {
            echo "✅ Loop Builder context detection works\n";
        } else {
            echo "❌ Loop Builder context detection failed\n";
        }
        
        // Test normal context (should not be detected as Loop Builder)
        $normal_context = [
            'postId' => 456,
            'postType' => 'page'
        ];
        
        $is_normal_context = $method->invoke($taxonomy_block, $normal_context);
        
        if (!$is_normal_context) {
            echo "✅ Normal context correctly not detected as Loop Builder\n";
        } else {
            echo "❌ Normal context incorrectly detected as Loop Builder\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test PHP backend integration
     */
    private static function test_php_backend_integration() {
        echo "<h3>3. Testing PHP Backend Integration</h3>\n";
        
        $taxonomy_block = new \EssentialBlocks\Blocks\Taxonomy();
        
        // Test get_post_id method with Loop Builder context
        $reflection = new ReflectionClass($taxonomy_block);
        $method = $reflection->getMethod('get_post_id');
        $method->setAccessible(true);
        
        $loop_context = [
            'essential-blocks/postId' => 789
        ];
        
        $post_id = $method->invoke($taxonomy_block, $loop_context, true);
        
        if ($post_id === 789) {
            echo "✅ Loop Builder post ID retrieval works\n";
        } else {
            echo "❌ Loop Builder post ID retrieval failed (got: {$post_id})\n";
        }
        
        // Test fallback to get_the_ID() when not in Loop Builder
        $normal_context = [];
        $fallback_id = $method->invoke($taxonomy_block, $normal_context, false);
        
        // Since we're not in a post context, this should return the current post ID or 0
        echo "✅ Fallback post ID retrieval works (got: {$fallback_id})\n";
        
        echo "\n";
    }
}

// Run tests if this file is accessed directly with the test parameter
if (isset($_GET['test']) && $_GET['test'] === 'taxonomy-loop-builder') {
    TaxonomyLoopBuilderTest::run_tests();
}
