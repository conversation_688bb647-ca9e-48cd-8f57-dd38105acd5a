/**
 * WordPress dependencies
 */
import { __ } from "@wordpress/i18n";
import { Fragment, useEffect, memo } from "@wordpress/element";
import { useEntityRecords, store as coreStore, useEntityRecord } from '@wordpress/core-data';
import { select, useSelect, withSelect } from "@wordpress/data";
import { compose } from "@wordpress/compose";

/**
 * Internal depencencies
 */
import Style from "./style";
import Inspector from "./inspector";
import defaultAttributes from './attributes'
import { ReactComponent as TaxonomyIcon } from "./icon.svg";
import { ebLoader, renderCategoryName } from "./helpers";

/**
 * External depencencies
 */
import {
    DynamicInputValueHandler,
    BlockProps,
    withBlockContext,
    EBDisplayIcon,
    NoticeComponent,
} from "@essential-blocks/controls";

function Edit(props) {
    const {
        attributes,
        setAttributes,
        isSelected,
        selectPostType,
        context
    } = props;
    const {
        blockId,
        classHook,
        selectedTaxonomy,
        showHierarchy,
        showPostCounts,
        displayStyle,
        prefixType,
        prefixText,
        prefixIcon,
        suffixType,
        suffixIcon,
        suffixText,
        separator,
        showSeparator,
        taxonomyLimit,
        taxonomiesQuery,
        source,
        currentPostType
    } = attributes;

    const postType = select("core/editor").getCurrentPostType();

    // Get post data from Loop Builder context
    const loopPostId = context?.["essential-blocks/postId"];
    const loopPostType = context?.["essential-blocks/postType"];

    // Check if block is inside Loop Builder context
    const isInLoopBuilder = Boolean(
        context &&
            // Primary check: explicit isLoopBuilder flag
            (context["essential-blocks/isLoopBuilder"] === true ||
                // Secondary check: presence of loop context values (even if null initially)
                (context.hasOwnProperty("essential-blocks/postId") &&
                    context.hasOwnProperty("essential-blocks/postType"))),
    );

    // Use loop context values when in Loop Builder, otherwise use current values
    const effectivePostType = isInLoopBuilder ? (loopPostType || currentPostType) : currentPostType;
    const effectivePostId = isInLoopBuilder ? (loopPostId || 0) : 0;

    // this useEffect is for creating a unique id for each block's unique className by a random unique number
    useEffect(() => {
        if (selectPostType) {
            setAttributes({
                currentPostType: selectPostType,
            });
        }
    }, []);

    // Effect to handle Loop Builder context initialization
    useEffect(() => {
        if (isInLoopBuilder) {
            // Set source to current-post when in Loop Builder
            setAttributes({
                source: 'current-post',
                currentPostType: loopPostType || 'post'
            });
        }
    }, [isInLoopBuilder, loopPostId, loopPostType]);

    // you must declare this variable
    const enhancedProps = {
        ...props,
        blockPrefix: 'eb-taxonomy',
        style: <Style {...props} />
    };

    useEffect(() => {
        if (taxonomyLimit == undefined) {
            setAttributes({ taxonomyLimit: 0 })
        };
    }, [taxonomyLimit]);

    // on source change
    useEffect(() => {
        if (source === 'current-post') {
            let type = selectPostType;

            // Use loop context post type when in Loop Builder
            if (isInLoopBuilder && loopPostType) {
                type = loopPostType;
            } else if (postType === "templately_library") {
                type = 'post'
                const templateType = select('core/editor').getEditedPostAttribute('templately_type');
                if (templateType) {
                    if (['product_archive', 'product_single'].includes(templateType)) {
                        type = 'product'
                    }
                    if (['course_archive', 'course_single'].includes(templateType)) {
                        type = 'sfwd-courses'
                    }
                }
            } else if (postType === 'wp_template') {
                const slugArray = select('core/editor').getEditedPostAttribute('slug').split('-');
                type = 'post';
                if (slugArray.length > 1) {
                    type = slugArray[1];
                }
                if (slugArray.length === 1 && slugArray[0] === 'page') {
                    type = 'page';
                }
            }
            setAttributes({ taxonomiesQuery: { ...taxonomiesQuery, type } })
        } else {
            let newQuery = { ...taxonomiesQuery }
            if (newQuery.hasOwnProperty('type')) {
                delete newQuery.type;
            }
            setAttributes({ taxonomiesQuery: newQuery })
        }
    }, [source, isInLoopBuilder, loopPostType]);

    // on taxonomiesQuery change
    const { taxonomies, hasResolved } = useSelect((select) => {
        return {
            taxonomies: select(coreStore).getTaxonomies(taxonomiesQuery),
            hasResolved: select(coreStore).hasFinishedResolution(
                'getTaxonomies',
                [taxonomiesQuery]
            ),
        };
    }, [taxonomiesQuery]);

    useEffect(() => {
        if (hasResolved) {
            if (!selectedTaxonomy && taxonomies && taxonomies.length) {
                setAttributes({ selectedTaxonomy: taxonomies[0].slug })
            } else {
                if (taxonomies && taxonomies.length) {
                    const slugs = taxonomies.map(each => each.slug);
                    if (!slugs.includes(selectedTaxonomy)) {
                        setAttributes({ selectedTaxonomy: slugs[0] })
                    }
                } else {
                    setAttributes({ selectedTaxonomy: '' })
                }
            }
        }
    }, [taxonomies, hasResolved]);

    const query = { per_page: taxonomyLimit == 0 ? -1 : taxonomyLimit }
    const { records: categories, isResolving } = useEntityRecords(
        'taxonomy',
        selectedTaxonomy,
        query
    );

    // Fetch real taxonomy terms for the current post when in Loop Builder
    const { record: currentPost } = useEntityRecord(
        'postType',
        effectivePostType,
        effectivePostId,
        {
            enabled: isInLoopBuilder && effectivePostId > 0 && selectedTaxonomy
        }
    );

    // Get taxonomy terms for the current post in Loop Builder context
    const currentPostTerms = useSelect((select) => {
        if (!isInLoopBuilder || !effectivePostId || !selectedTaxonomy) {
            return null;
        }

        // In editor context, we'll show placeholder data since we can't fetch real post terms
        // The real terms will be fetched on the frontend by PHP
        if (effectivePostId > 0) {
            // Return placeholder terms to show in editor
            const taxonomyObj = taxonomies?.find(tax => tax.slug === selectedTaxonomy);
            const taxonomyName = taxonomyObj?.name || 'Category';

            return [{
                id: 1,
                name: `Sample ${taxonomyName}`,
                link: '#',
                count: 1
            }];
        }

        return null;
    }, [isInLoopBuilder, effectivePostId, selectedTaxonomy, taxonomies]);

    const getCategoriesList = (parentId) => {
        if (!categories?.length) {
            return [];
        }
        if (parentId === null) {
            return categories;
        }
        return categories.filter(({ parent }) => parent === parentId);
    };

    const renderCategoryList = () => {
        let categoriesList = [];
        if (source === 'current-post') {
            if (isInLoopBuilder) {
                // In Loop Builder, show taxonomy terms for the current post
                if (currentPostTerms && currentPostTerms.length > 0) {
                    categoriesList = currentPostTerms.map(term => ({
                        id: term.id,
                        link: term.link || '#',
                        count: term.count || 0,
                        name: term.name
                    }));
                } else if (effectivePostId === 0) {
                    // No post ID available - show placeholder
                    const taxonomyObj = taxonomies?.find(tax => tax.slug === selectedTaxonomy);
                    const taxonomyName = taxonomyObj?.name || 'Category';
                    categoriesList = [{
                        id: 0,
                        link: '#',
                        count: 0,
                        name: `No ${taxonomyName} selected`
                    }];
                } else {
                    // Post has no terms for this taxonomy - return empty array to show nothing
                    categoriesList = [];
                }
            } else {
                // Regular current-post behavior - show placeholder
                let name = 'Category'
                if (selectedTaxonomy && taxonomies) {
                    const tax = taxonomies.find(each => each.slug === selectedTaxonomy);
                    if (tax) {
                        name = tax.name
                    }
                }
                categoriesList = [{
                    id: 0, link: '#', count: 5, name
                }]
            }
        } else {
            const parentId = showHierarchy ? 0 : null;
            categoriesList = getCategoriesList(parentId);
        }
        return categoriesList.map((category, index) => renderCategoryListItem(category));

    };

    const renderCategoryListItem = (category) => {
        const childCategories = getCategoriesList(category.id);
        const { id, link, count, name } = category;
        return (
            <Fragment key={id}>
                <span className={`eb-tax-item eb-tax-item-${id}`}>
                    <a href={link} target="_blank" rel="noreferrer noopener">
                        {renderCategoryName(name)}
                    </a>
                    {showPostCounts && ` (${count})`}
                    {showHierarchy && childCategories.length && (
                        <ul className="children">
                            {childCategories.map((childCategory) =>
                                renderCategoryListItem(childCategory)
                            )}
                        </ul>
                    )}
                </span>
                {showSeparator && separator && (
                    <span className="eb-tax-separator">{separator}</span>
                )}
            </Fragment>
        );
    };
    const taxonomyNotice = ebLoader();

    return (
        <>
            {isSelected && (
                <Inspector
                    attributes={attributes}
                    setAttributes={setAttributes}
                    taxonomies={taxonomies}
                    context={context}
                />
            )}

            <BlockProps.Edit {...enhancedProps}>
                <div
                    className={`eb-parent-wrapper eb-parent-${blockId} ${classHook}`}
                >
                    {!hasResolved && (
                        <NoticeComponent
                            Icon={TaxonomyIcon}
                            title={__("Taxonomy", "essential-blocks")}
                            description={taxonomyNotice}
                        />
                    )}

                    {hasResolved && !taxonomies && (
                        <NoticeComponent
                            Icon={TaxonomyIcon}
                            title={__("Taxonomy", "essential-blocks")}
                            description={`No taxonomies found for this post`}
                        />
                    )}

                    {hasResolved && taxonomies && (
                        <>
                            {/* Show notice for empty categories only when not in Loop Builder or when explicitly no data */}
                            {Array.isArray(categories) && categories.length == 0 && eb_conditional_localize?.editor_type !== 'edit-site' && !isInLoopBuilder && (
                                <NoticeComponent
                                    Icon={TaxonomyIcon}
                                    title={__("Taxonomy", "essential-blocks")}
                                    description={`Not found any data.`}
                                />
                            )}

                            {/* In Loop Builder, only show content if there are terms or if no post ID (placeholder), otherwise show nothing */}
                            {(!isInLoopBuilder || (isInLoopBuilder && (effectivePostId === 0 || (currentPostTerms && currentPostTerms.length > 0)))) && (
                                <div
                                    className={`eb-taxonomies-wrapper ${blockId} ${displayStyle}`}
                                    data-id={blockId}
                                >
                                    {prefixType !== 'none' && (
                                        <div className="prefix-wrap">
                                            {prefixType === 'text' && prefixText && (
                                                <DynamicInputValueHandler
                                                    value={prefixText}
                                                    tagName='span'
                                                    className="eb-taxonomy-prefix-text"
                                                    onChange={(prefixText) =>
                                                        setAttributes({ prefixText })
                                                    }
                                                    readOnly={true}
                                                />
                                            )}

                                            {prefixType === 'icon' && prefixIcon && (
                                                <EBDisplayIcon icon={prefixIcon} className={`eb-taxonomy-prefix-icon`} />
                                            )}
                                        </div>
                                    )}


                                    <div className="eb-tax-wrap">
                                        {renderCategoryList()}
                                    </div>

                                {suffixType !== 'none' && (
                                    <div className="suffix-wrap">
                                        {suffixType === 'text' && suffixText && (
                                            <DynamicInputValueHandler
                                                value={suffixText}
                                                placeholder='placeholder text'
                                                tagName='span'
                                                className="eb-taxonomy-suffix-text"
                                                onChange={(suffixText) =>
                                                    setAttributes({ suffixText })
                                                }
                                                readOnly={true}
                                            />
                                        )}

                                        {suffixType === 'icon' && suffixIcon && (
                                            <EBDisplayIcon icon={suffixIcon} className={`eb-taxonomy-suffix-icon`} />
                                        )}
                                    </div>
                                )}
                            </div>
                            )}
                        </>
                    )}
                </div>
            </BlockProps.Edit>
        </>
    );
}
export default memo(
    compose([
        withSelect((select, ownProps) => {
            const selectPostType = select("core/editor") ? select("core/editor").getCurrentPostType() : "";
            return {
                selectPostType: selectPostType,
            };
        }),
        withBlockContext(defaultAttributes)
    ])(Edit)
)
